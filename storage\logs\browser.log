[2025-08-31 18:06:42] local.ERROR: Uncaught TypeError: n.toFixed is not a function http://127.0.0.1:8000/build/assets/withdraw-DfbGtcRl.js 6 1504 TypeError n.toFixed is not a function TypeError: n.toFixed is not a function
    at le (http://127.0.0.1:8000/build/assets/withdraw-DfbGtcRl.js:6:1504)
    at $o (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:34139)
    at os (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:62258)
    at sp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:72760)
    at Bp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:106782)
    at Ob (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:105848)
    at Ns (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:105680)
    at Cp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:102792)
    at Fp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:114218)
    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:99:1597) {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:06:41.796Z"} 
[2025-08-31 18:06:42] local.ERROR: Uncaught TypeError: n.toFixed is not a function http://127.0.0.1:8000/build/assets/withdraw-DfbGtcRl.js 6 1504 TypeError n.toFixed is not a function TypeError: n.toFixed is not a function
    at le (http://127.0.0.1:8000/build/assets/withdraw-DfbGtcRl.js:6:1504)
    at $o (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:34139)
    at os (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:62258)
    at sp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:72760)
    at Bp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:106782)
    at Ob (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:105848)
    at Ns (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:105680)
    at Cp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:102792)
    at Fp (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:122:114218)
    at MessagePort.ve (http://127.0.0.1:8000/build/assets/app-C-PwF_3Z.js:99:1597) {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:06:41.796Z"} 
[2025-08-31 18:13:37] local.ERROR: Uncaught TypeError: balance.toFixed is not a function http://[::1]:5173/resources/js/pages/wallet/withdraw.tsx 114 21 TypeError balance.toFixed is not a function TypeError: balance.toFixed is not a function
    at Withdraw (http://[::1]:5173/resources/js/pages/wallet/withdraw.tsx:114:21)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10359:46) {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:13:37.069Z"} 
[2025-08-31 18:13:37] local.ERROR: Uncaught TypeError: balance.toFixed is not a function http://[::1]:5173/resources/js/pages/wallet/withdraw.tsx 114 21 TypeError balance.toFixed is not a function TypeError: balance.toFixed is not a function
    at Withdraw (http://[::1]:5173/resources/js/pages/wallet/withdraw.tsx:114:21)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=9e5930d7:10359:46) {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:13:37.070Z"} 
[2025-08-31 18:13:37] local.WARNING: %s

%s An error occurred in the <Withdraw> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/wallet/withdraw","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:13:37.070Z"} 
[2025-08-31 18:28:44] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-08-31T18:28:43.608Z"} 
[2025-09-01 11:48:04] local.ERROR: Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}? true inert Although this works, it will not work as expected if you pass the string "false". inert true {"url":"http://127.0.0.1:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T11:47:58.894Z"} 
[2025-09-01 11:56:29] local.ERROR: Received the string `%s` for the boolean attribute `%s`. %s Did you mean %s={%s}? true inert Although this works, it will not work as expected if you pass the string "false". inert true {"url":"http://127.0.0.1:8000/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T11:56:27.396Z"} 
[2025-09-01 12:04:00] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T12:00:09.191Z"} 
[2025-09-01 12:27:12] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T12:27:12.202Z"} 
[2025-09-01 21:35:34] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/my-projects","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T12:49:17.172Z"} 
[2025-09-01 22:08:28] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:08:27.197Z"} 
[2025-09-01 22:12:10] local.ERROR: In HTML, %s cannot be a descendant of <%s>.
This will cause a hydration error.%s <ul> p ...
    <FocusScope asChild={true} loop={true} trapped={true} onMountAutoFocus={undefined} ...>
      <Primitive.div tabIndex={-1} asChild={true} ref={function} onKeyDown={function}>
        <Primitive.div.Slot tabIndex={-1} onKeyDown={function} ref={function}>
          <Primitive.div.SlotClone tabIndex={-1} onKeyDown={function} ref={function}>
            <DismissableLayer role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
              <Primitive.div role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
                <div role="dialog" id="radix-«r1h»" aria-describedby="radix-«r1j»" aria-labelledby="radix-«r1i»" ...>
                  <DialogHeader>
                    <div data-slot="dialog-header" className="flex flex-...">
                      <DialogTitle>
                      <DialogDescription>
                        <DialogDescription data-slot="dialog-des..." className="text-muted...">
                          <Primitive.p id="radix-«r1j»" data-slot="dialog-des..." className="text-muted..." ref={null}>
>                           <p
>                             id="radix-«r1j»"
>                             data-slot="dialog-description"
>                             className="text-muted-foreground text-sm"
>                             ref={null}
>                           >
                              <strong>
                              <br>
                              <br>
                              <span>
>                             <ul className="mt-2 list-inside list-disc space-y-1 text-sm">
                  ...
                  ...
    ... {"url":"http://127.0.0.1:8000/projects/legal-chat-bot-for-ghana","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:12:09.453Z"} 
[2025-09-01 22:12:10] local.ERROR: <%s> cannot contain a nested %s.
See this log for the ancestor stack trace. p <ul> {"url":"http://127.0.0.1:8000/projects/legal-chat-bot-for-ghana","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:12:09.453Z"} 
[2025-09-01 22:20:03] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:20:01.046Z"} 
[2025-09-01 22:20:08] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:20:06.715Z"} 
[2025-09-01 22:20:14] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-01T22:20:13.073Z"} 
[2025-09-02 07:24:55] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:24:52.519Z"} 
[2025-09-02 07:30:02] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-02T07:29:58.413Z"} 
[2025-09-02 07:40:07] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:40:04.926Z"} 
[2025-09-02 07:47:08] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:47:07.430Z"} 
[2025-09-02 07:47:48] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:47:48.629Z"} 
[2025-09-02 07:49:25] local.ERROR: Payment error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:49:02.652Z"} 
[2025-09-02 07:50:51] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:50:51.014Z"} 
[2025-09-02 07:52:22] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:52:22.284Z"} 
[2025-09-02 07:56:06] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:56:06.332Z"} 
[2025-09-02 07:57:28] local.WARNING: Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}. {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:57:28.664Z"} 
[2025-09-02 07:59:24] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T07:59:24.754Z"} 
[2025-09-02 07:59:41] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","timestamp":"2025-09-02T07:59:41.103Z"} 
[2025-09-02 08:01:05] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:01:05.560Z"} 
[2025-09-02 08:07:19] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:07:18.720Z"} 
[2025-09-02 08:07:24] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:07:24.555Z"} 
[2025-09-02 08:07:54] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:07:54.273Z"} 
[2025-09-02 08:09:30] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/browse","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:09:30.282Z"} 
[2025-09-02 08:12:01] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:12:00.667Z"} 
[2025-09-02 08:12:51] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:12:51.182Z"} 
[2025-09-02 08:13:06] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:13:06.103Z"} 
[2025-09-02 08:13:21] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:13:21.715Z"} 
[2025-09-02 08:13:39] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/balance","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:13:38.936Z"} 
[2025-09-02 08:13:52] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/transactions","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:13:52.615Z"} 
[2025-09-02 08:14:19] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:14:18.490Z"} 
[2025-09-02 08:14:38] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:14:38.026Z"} 
[2025-09-02 08:14:48] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:14:47.975Z"} 
[2025-09-02 08:15:30] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:15:30.694Z"} 
[2025-09-02 08:18:34] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:18:34.325Z"} 
[2025-09-02 08:18:54] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:18:53.477Z"} 
[2025-09-02 08:19:09] local.ERROR: Unhandled Promise Rejection AxiosError Network Error AxiosError: Network Error
    at XMLHttpRequest.handleError (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:1615:14)
    at Axios.request (http://[::1]:5173/node_modules/.vite/deps/chunk-HUJ6OB47.js?v=a3286eb9:2143:41) {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:19:09.249Z"} 
[2025-09-02 08:22:16] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:22:16.466Z"} 
[2025-09-02 08:24:50] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:24:49.487Z"} 
[2025-09-02 08:25:46] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:25:46.040Z"} 
[2025-09-02 08:26:44] local.INFO: %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold {"url":"http://localhost:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:26:43.852Z"} 
[2025-09-02 08:36:09] local.ERROR: Error: SyntaxError Unexpected token '<', "<!DOCTYPE "... is not valid JSON SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON {"url":"http://127.0.0.1:8000/wallet/add-funds","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T08:36:04.864Z"} 
[2025-09-02 18:16:29] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T18:16:28.972Z"} 
[2025-09-02 18:24:35] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/football-flutter-mobile-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T18:24:34.817Z"} 
[2025-09-02 18:24:36] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/football-flutter-mobile-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T18:24:34.821Z"} 
[2025-09-02 18:36:17] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://localhost:8000/projects/flutter-sports-app","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","timestamp":"2025-09-02T18:36:16.033Z"} 
