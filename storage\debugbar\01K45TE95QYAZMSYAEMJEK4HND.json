{"__meta": {"id": "01K45TE95QYAZMSYAEMJEK4HND", "datetime": "2025-09-02 18:18:47", "utime": **********.353076, "method": "POST", "uri": "/projects", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.452258, "end": **********.353106, "duration": 0.9008479118347168, "duration_str": "901ms", "measures": [{"label": "Booting", "start": **********.452258, "relative_start": 0, "end": **********.870341, "relative_end": **********.870341, "duration": 0.*****************, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.87037, "relative_start": 0.*****************, "end": **********.353109, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "483ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.925935, "relative_start": 0.****************, "end": **********.93217, "relative_end": **********.93217, "duration": 0.006234884262084961, "duration_str": "6.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.344026, "relative_start": 0.****************, "end": **********.345374, "relative_end": **********.345374, "duration": 0.0013480186462402344, "duration_str": "1.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.18331999999999998, "accumulated_duration_str": "183ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 219}], "start": **********.967707, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'i1slcmXV07OtSreSUuEpgaoLk9NQw6BSVrKCGABi' limit 1", "type": "query", "params": [], "bindings": ["i1slcmXV07OtSreSUuEpgaoLk9NQw6BSVrKCGABi"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.977941, "duration": 0.11011, "duration_str": "110ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "thesylink", "explain": null, "start_percent": 0, "width_percent": 60.064}, {"sql": "select * from \"users\" where \"id\" = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.117413, "duration": 0.01089, "duration_str": "10.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "thesylink", "explain": null, "start_percent": 60.064, "width_percent": 5.94}, {"sql": "select exists(select * from \"projects\" where \"slug\" = 'football-flutter-mobile-app') as \"exists\"", "type": "query", "params": [], "bindings": ["football-flutter-mobile-app"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Models/Project.php", "file": "C:\\dev\\thesylink\\app\\Models\\Project.php", "line": 77}, {"index": 12, "namespace": null, "name": "app/Models/Project.php", "file": "C:\\dev\\thesylink\\app\\Models\\Project.php", "line": 60}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/ProjectController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\ProjectController.php", "line": 101}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.2216911, "duration": 0.0062900000000000005, "duration_str": "6.29ms", "memory": 0, "memory_str": null, "filename": "Project.php:77", "source": {"index": 11, "namespace": null, "name": "app/Models/Project.php", "file": "C:\\dev\\thesylink\\app\\Models\\Project.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=77", "ajax": false, "filename": "Project.php", "line": "77"}, "connection": "thesylink", "explain": null, "start_percent": 66.005, "width_percent": 3.431}, {"sql": "insert into \"projects\" (\"user_id\", \"title\", \"description\", \"requirements\", \"budget_min\", \"budget_max\", \"budget_type\", \"deadline\", \"category\", \"academic_level\", \"slug\", \"updated_at\", \"created_at\") values (13, 'Football Flutter Mobile app', 'I need this app for my company for sporting activities', 'dart/Flutter', '4000', '5000', 'fixed', '2025-09-26 00:00:00', 'Literature', 'Professional', 'football-flutter-mobile-app', '2025-09-02 18:18:47', '2025-09-02 18:18:47') returning \"id\"", "type": "query", "params": [], "bindings": [13, "Football Flutter Mobile app", "I need this app for my company for sporting activities", "dart/Flutter", "4000", "5000", "fixed", "2025-09-26 00:00:00", "Literature", "Professional", "football-flutter-mobile-app", "2025-09-02 18:18:47", "2025-09-02 18:18:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/ProjectController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\ProjectController.php", "line": 101}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.23327, "duration": 0.03358, "duration_str": "33.58ms", "memory": 0, "memory_str": null, "filename": "ProjectController.php:101", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/ProjectController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\ProjectController.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FProjectController.php&line=101", "ajax": false, "filename": "ProjectController.php", "line": "101"}, "connection": "thesylink", "explain": null, "start_percent": 69.436, "width_percent": 18.318}, {"sql": "insert into \"project_files\" (\"project_id\", \"filename\", \"original_name\", \"file_path\", \"mime_type\", \"file_size\", \"updated_at\", \"created_at\") values (15, '**********_0.pdf', 'Gilles_CV.pdf', 'project-files/**********_0.pdf', 'application/pdf', 249534, '2025-09-02 18:18:47', '2025-09-02 18:18:47') returning \"id\"", "type": "query", "params": [], "bindings": [15, "**********_0.pdf", "Gilles_CV.pdf", "project-files/**********_0.pdf", "application/pdf", 249534, "2025-09-02 18:18:47", "2025-09-02 18:18:47"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/ProjectController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\ProjectController.php", "line": 113}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.306754, "duration": 0.01541, "duration_str": "15.41ms", "memory": 0, "memory_str": null, "filename": "ProjectController.php:113", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/ProjectController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\ProjectController.php", "line": 113}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FProjectController.php&line=113", "ajax": false, "filename": "ProjectController.php", "line": "113"}, "connection": "thesylink", "explain": null, "start_percent": 87.754, "width_percent": 8.406}, {"sql": "update \"projects\" set \"file_count\" = 1, \"updated_at\" = '2025-09-02 18:18:47' where \"id\" = 15", "type": "query", "params": [], "bindings": [1, "2025-09-02 18:18:47", 15], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProjectController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\ProjectController.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 822}], "start": **********.325077, "duration": 0.00704, "duration_str": "7.04ms", "memory": 0, "memory_str": null, "filename": "ProjectController.php:125", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProjectController.php", "file": "C:\\dev\\thesylink\\app\\Http\\Controllers\\ProjectController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FProjectController.php&line=125", "ajax": false, "filename": "ProjectController.php", "line": "125"}, "connection": "thesylink", "explain": null, "start_percent": 96.16, "width_percent": 3.84}]}, "models": {"data": {"App\\Models\\Project": {"created": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProject.php&line=1", "ajax": false, "filename": "Project.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProjectFile": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FModels%2FProjectFile.php&line=1", "ajax": false, "filename": "ProjectFile.php", "line": "?"}}}, "count": 4, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1, "created": 2, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/projects", "action_name": "projects.store", "controller_action": "App\\Http\\Controllers\\ProjectController@store", "uri": "POST projects", "controller": "App\\Http\\Controllers\\ProjectController@store<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FProjectController.php&line=97\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fapp%2FHttp%2FControllers%2FProjectController.php&line=97\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ProjectController.php:97-129</a>", "middleware": "web, auth, verified", "duration": "904ms", "peak_memory": "30MB", "response": "Redirect to http://localhost:8000/dashboard", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1474913695 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1474913695\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Football Flutter Mobile app</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"54 characters\">I need this app for my company for sporting activities</span>\"\n  \"<span class=sf-dump-key>requirements</span>\" => \"<span class=sf-dump-str title=\"12 characters\">dart/Flutter</span>\"\n  \"<span class=sf-dump-key>budget_min</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4000</span>\"\n  \"<span class=sf-dump-key>budget_max</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5000</span>\"\n  \"<span class=sf-dump-key>budget_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">fixed</span>\"\n  \"<span class=sf-dump-key>deadline</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-09-26</span>\"\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Literature</span>\"\n  \"<span class=sf-dump-key>academic_level</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Professional</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1397879654 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">250740</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjBUNTFiV29XcjZMOHY1NWdIZkZyV1E9PSIsInZhbHVlIjoiNnhiVHNlZjltYTJOZzQ4Ymp5eDZlVTZDeHUrdnAwWExUTmllNHpScCtLUXczbE1QcjZaUVZwOTZMZUdrM3R2QUFVbG02U3o5YzNsL0JJbXRPemFBNFhLcytUelZIZi9IY0NzWU51UWVkalVzM0szelAvSHhscHJzR09ueEhNY04iLCJtYWMiOiI2YjZkMjQyNDNiNzA0MGIyZWFkOWYwY2E2YWY2NTFlNDkxNmJkZTdkYTQ2ZjgxMjdkOGE2YTgyNjBiNGMyYTAwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryHKrNsiV6re3tfFTm</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/projects/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1390 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlY0NkYzT1JIU0NDbWpWNy9xVlZWM2c9PSIsInZhbHVlIjoib3VvUnVlSForNjdJeFkzVVJzWjZDMktScDBpbVFQaXQ5OSs3MFhxcjROYmMwREg5VXptcXptUnlJK3pIeEhhV1RNQVJJeDBDeXNlcE9xa1FOYS9aU3FNNHNjNTBpazhScXZacU9jOStNOEt3bFNLYnN6NVV0QnBWYVdrOTRXOEdzdHZuS2JtZ1RianFPaERaU2JUYWtBPT0iLCJtYWMiOiJiYzU3NWJmZjgwZTcxZTQyOTAwZGY2ODIwNWU1YjJiYjczMmQ1ZTEzNWU3ZGQ4MGU0MWE5NjQ0ZWU1ZGVmMjU1IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjBUNTFiV29XcjZMOHY1NWdIZkZyV1E9PSIsInZhbHVlIjoiNnhiVHNlZjltYTJOZzQ4Ymp5eDZlVTZDeHUrdnAwWExUTmllNHpScCtLUXczbE1QcjZaUVZwOTZMZUdrM3R2QUFVbG02U3o5YzNsL0JJbXRPemFBNFhLcytUelZIZi9IY0NzWU51UWVkalVzM0szelAvSHhscHJzR09ueEhNY04iLCJtYWMiOiI2YjZkMjQyNDNiNzA0MGIyZWFkOWYwY2E2YWY2NTFlNDkxNmJkZTdkYTQ2ZjgxMjdkOGE2YTgyNjBiNGMyYTAwIiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6IkpTeWdkK1pQL21XNUNINzJieEZWY1E9PSIsInZhbHVlIjoic3dqekZQRlcvcDJic2VDaWtCaVkvSzhoN0k4YjVKdm1tSndPZ1UrNlpJSDk2YkRPTlgwNVFod0F1YWg5cm1jbEFoWjE2N0MyVC9rVDgrc0psWHhHUXl0aE1oaWFDSHk3V2dZamYwZC93b29xQUIzSDNDQkdGVDZjeFg3UUVReTAiLCJtYWMiOiIyYjUwMDBlNTBlNDMxNmY0ZmMxZjkzMzY0MjM0NmRlZjc0ZjI4YWZlMTc2YTMyNDkzYjU3ZGZlOWJjZmM3OTBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397879654\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1024304264 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"64 characters\">13|j0CYnq9hfN17FsMZiTrA9vdSvG5CT3BpLMyc0gJdU2xl9u5vZSisAeFbQ7MB|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jaOc9BjTDgM5WO6TibszeiiUMGpg9AH4SeC0Df8W</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">i1slcmXV07OtSreSUuEpgaoLk9NQw6BSVrKCGABi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024304264\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1569973963 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 02 Sep 2025 18:18:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569973963\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1295366155 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jaOc9BjTDgM5WO6TibszeiiUMGpg9AH4SeC0Df8W</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>13</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1295366155\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/projects", "action_name": "projects.store", "controller_action": "App\\Http\\Controllers\\ProjectController@store"}, "badge": "302 Found"}}