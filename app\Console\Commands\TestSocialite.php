<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON>\Socialite\Facades\Socialite;

class TestSocialite extends Command
{
    protected $signature = 'test:socialite';
    protected $description = 'Test if Socialite is working';

    public function handle()
    {
        try {
            $this->info('Testing Socialite...');

            // Test if the class exists
            if (class_exists('Laravel\Socialite\Facades\Socialite')) {
                $this->info('✓ Socialite facade class exists');
            } else {
                $this->error('✗ Socialite facade class does not exist');
                return 1;
            }

            // Test if we can create a Google driver
            $driver = Socialite::driver('google');
            $this->info('✓ Google driver created successfully');

            // Test redirect URL generation
            $redirectUrl = $driver->redirect()->getTargetUrl();
            $this->info('✓ Redirect URL generated: ' . substr($redirectUrl, 0, 50) . '...');

            $this->info('Socialite is working correctly!');
            return 0;
        } catch (\Exception $e) {
            $this->error('✗ Error: ' . $e->getMessage());
            return 1;
        }
    }
}
