{"__meta": {"id": "01K45TRXW2YWJVY92BB9X1Q1K9", "datetime": "2025-09-02 18:24:36", "utime": **********.227614, "method": "POST", "uri": "/_boost/browser-logs", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[18:24:36] LOG.debug: [vite] server connection lost. Polling for restart... {\n    \"url\": \"http:\\/\\/localhost:8000\\/projects\\/football-flutter-mobile-app\",\n    \"user_agent\": \"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\",\n    \"timestamp\": \"2025-09-02T18:24:34.821Z\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.211625, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": 1756837475.974485, "end": **********.227634, "duration": 0.25314903259277344, "duration_str": "253ms", "measures": [{"label": "Booting", "start": 1756837475.974485, "relative_start": 0, "end": **********.187625, "relative_end": **********.187625, "duration": 0.*****************, "duration_str": "213ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.187636, "relative_start": 0.****************, "end": **********.227637, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "40ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.20624, "relative_start": 0.*****************, "end": **********.20848, "relative_end": **********.20848, "duration": 0.0022399425506591797, "duration_str": "2.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.225289, "relative_start": 0.*****************, "end": **********.225635, "relative_end": **********.225635, "duration": 0.0003459453582763672, "duration_str": "346μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.225659, "relative_start": 0.****************, "end": **********.225678, "relative_end": **********.225678, "duration": 1.9073486328125e-05, "duration_str": "19μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.27.0", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 0, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionServiceProvider.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php", "line": 52}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1154}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 972}], "start": **********.221874, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\dev\\thesylink\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "thesylink", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure", "uri": "POST _boost/browser-logs", "excluded_middleware": ["Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken"], "file": "<a href=\"phpstorm://open?file=C%3A%2Fdev%2Fthesylink%2Fvendor%2Flaravel%2Fboost%2Fsrc%2FBoostServiceProvider.php&line=101\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/boost/src/BoostServiceProvider.php:101-127</a>", "duration": "254ms", "peak_memory": "22MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-664980445 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>logs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">log</span>\"\n      \"<span class=sf-dump-key>timestamp</span>\" => \"<span class=sf-dump-str title=\"24 characters\">2025-09-02T18:24:34.821Z</span>\"\n      \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">[vite] server connection lost. Polling for restart...</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"58 characters\">http://localhost:8000/projects/football-flutter-mobile-app</span>\"\n      \"<span class=sf-dump-key>userAgent</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664980445\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">322</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">http://localhost:8000/projects/football-flutter-mobile-app</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"960 characters\">mintlify-auth-key=ba5658bd4fdaa31823eff4f3ddd8fd98; PGADMIN_LANGUAGE=en; phpMyAdmin=6ed91cf578d851379b68c861c0577493; pma_lang=en; appearance=light; pga4_session=cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=; XSRF-TOKEN=eyJpdiI6Im1RVG1wZkpYeUJqaUp2dGdPQXpGd0E9PSIsInZhbHVlIjoibHV3WDhxT1I4aDZVeHJHQVMrallrYk83M0N1Nk0wTklDbHp2a2U4d0szYnFsSkN5cTNwS3ZKUVlNU21SYjY3RGhtYnhTbER6SnR6MEFWRkxRdVdTL1JxL1hIVVVoL2hrZFVHU21hNlEvOENETDB4N2plYVpMczE2cjZJbzVnNEwiLCJtYWMiOiIzYjhjYWE2NWYwZDc4ZDhhNTYyMWQzMDg5YjJmZjE1MmU3ZTdhMjllYzQ4ZjdiMzVlZTE5MTAxNTg5YTBkNTNhIiwidGFnIjoiIn0%3D; thesylink_session=eyJpdiI6ImF3dTUzYk5Qdmk0MkkyZFNHME8vRWc9PSIsInZhbHVlIjoiYWY3Wi9abm8rUHRQQll1WnlpS01UdWFRV0dxekkwazRRZ1VWdVBDU2tNMFlTakZaSnlQWFdpTWRwVy83dFU4NkpJa3oxUDN4SjZvbWZnTDN0dTl0N1hEaWxpYnd0MktJbUdObm0zZFd6WlBLcThxc1BpcHVta3JyRW1ldjlsdXEiLCJtYWMiOiI4YTkyNzg3N2YwZWFhMjUzOTNlMjZmZmFmZmEzYjU3MTIxNGU4YzIyZDhiMTdmM2NiNTJmMDUzNDBjZjQwNTU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1200747967 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>mintlify-auth-key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">ba5658bd4fdaa31823eff4f3ddd8fd98</span>\"\n  \"<span class=sf-dump-key>PGADMIN_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>phpMyAdmin</span>\" => \"<span class=sf-dump-str title=\"32 characters\">6ed91cf578d851379b68c861c0577493</span>\"\n  \"<span class=sf-dump-key>pma_lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>pga4_session</span>\" => \"<span class=sf-dump-str title=\"81 characters\">cf383758-99aa-49b5-b2d3-4adac664796a!dIr5YgibG5rMppM5V6FeFAab9rtA3APd5lTD9hHPPXk=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1RVG1wZkpYeUJqaUp2dGdPQXpGd0E9PSIsInZhbHVlIjoibHV3WDhxT1I4aDZVeHJHQVMrallrYk83M0N1Nk0wTklDbHp2a2U4d0szYnFsSkN5cTNwS3ZKUVlNU21SYjY3RGhtYnhTbER6SnR6MEFWRkxRdVdTL1JxL1hIVVVoL2hrZFVHU21hNlEvOENETDB4N2plYVpMczE2cjZJbzVnNEwiLCJtYWMiOiIzYjhjYWE2NWYwZDc4ZDhhNTYyMWQzMDg5YjJmZjE1MmU3ZTdhMjllYzQ4ZjdiMzVlZTE5MTAxNTg5YTBkNTNhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>thesylink_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImF3dTUzYk5Qdmk0MkkyZFNHME8vRWc9PSIsInZhbHVlIjoiYWY3Wi9abm8rUHRQQll1WnlpS01UdWFRV0dxekkwazRRZ1VWdVBDU2tNMFlTakZaSnlQWFdpTWRwVy83dFU4NkpJa3oxUDN4SjZvbWZnTDN0dTl0N1hEaWxpYnd0MktJbUdObm0zZFd6WlBLcThxc1BpcHVta3JyRW1ldjlsdXEiLCJtYWMiOiI4YTkyNzg3N2YwZWFhMjUzOTNlMjZmZmFmZmEzYjU3MTIxNGU4YzIyZDhiMTdmM2NiNTJmMDUzNDBjZjQwNTU5IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200747967\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1492945445 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 02 Sep 2025 18:24:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1492945445\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-748763733 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-748763733\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/_boost/browser-logs", "action_name": "boost.browser-logs", "controller_action": "Closure"}, "badge": null}}